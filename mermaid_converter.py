#!/usr/bin/env python3
"""
Mermaid图表转换工具
支持转换为PNG格式，并可选择在终端打印质量信息
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
from typing import Optional, Tuple

class MermaidConverter:
    def __init__(self):
        self.supported_formats = ['png', 'svg', 'pdf']
        self.quality_levels = {
            'low': {'scale': 1, 'width': 800},
            'medium': {'scale': 2, 'width': 1200}, 
            'high': {'scale': 3, 'width': 1600},
            'ultra': {'scale': 4, 'width': 2400}
        }
    
    def check_mermaid_cli(self) -> bool:
        """检查是否安装了mermaid-cli"""
        try:
            result = subprocess.run(['mmdc', '--version'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def install_mermaid_cli(self) -> bool:
        """尝试安装mermaid-cli"""
        print("正在安装 mermaid-cli...")
        try:
            subprocess.run(['npm', 'install', '-g', '@mermaid-js/mermaid-cli'], 
                          check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ 安装失败。请手动安装:")
            print("npm install -g @mermaid-js/mermaid-cli")
            return False
    
    def convert_to_png(self, input_file: str, output_file: str, 
                      quality: str = 'medium', verbose: bool = False) -> bool:
        """转换Mermaid文件为PNG"""
        
        if not Path(input_file).exists():
            print(f"❌ 输入文件不存在: {input_file}")
            return False
        
        # 检查mermaid-cli
        if not self.check_mermaid_cli():
            print("❌ 未找到 mermaid-cli")
            if not self.install_mermaid_cli():
                return False
        
        # 获取质量参数
        quality_config = self.quality_levels.get(quality, self.quality_levels['medium'])
        
        # 构建命令
        cmd = [
            'mmdc',
            '-i', input_file,
            '-o', output_file,
            '-s', str(quality_config['scale']),
            '-w', str(quality_config['width']),
            '--backgroundColor', 'white'
        ]
        
        try:
            if verbose:
                print(f"🔄 执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                file_size = Path(output_file).stat().st_size
                print(f"✅ 转换成功!")
                print(f"📁 输出文件: {output_file}")
                print(f"📊 文件大小: {file_size / 1024:.1f} KB")
                print(f"🎨 质量等级: {quality}")
                print(f"📐 分辨率: {quality_config['width']}px (scale: {quality_config['scale']})")
                return True
            else:
                print(f"❌ 转换失败:")
                print(f"错误信息: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 执行出错: {e}")
            return False
    
    def print_file_info(self, file_path: str):
        """打印文件信息"""
        if not Path(file_path).exists():
            print(f"❌ 文件不存在: {file_path}")
            return
        
        path = Path(file_path)
        stat = path.stat()
        
        print(f"\n📄 文件信息:")
        print(f"   文件名: {path.name}")
        print(f"   路径: {path.absolute()}")
        print(f"   大小: {stat.st_size / 1024:.1f} KB")
        print(f"   修改时间: {stat.st_mtime}")
        
        # 读取文件内容统计
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.count('\n') + 1
                chars = len(content)
                print(f"   行数: {lines}")
                print(f"   字符数: {chars}")
        except Exception as e:
            print(f"   读取失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='Mermaid图表转PNG工具')
    parser.add_argument('input', help='输入的Mermaid文件路径')
    parser.add_argument('-o', '--output', help='输出PNG文件路径')
    parser.add_argument('-q', '--quality', 
                       choices=['low', 'medium', 'high', 'ultra'],
                       default='medium', help='图片质量 (默认: medium)')
    parser.add_argument('-v', '--verbose', action='store_true', 
                       help='显示详细信息')
    parser.add_argument('--info', action='store_true', 
                       help='显示输入文件信息')
    
    args = parser.parse_args()
    
    converter = MermaidConverter()
    
    # 显示文件信息
    if args.info:
        converter.print_file_info(args.input)
    
    # 生成输出文件名
    if not args.output:
        input_path = Path(args.input)
        args.output = str(input_path.with_suffix('.png'))
    
    # 转换文件
    success = converter.convert_to_png(
        args.input, 
        args.output, 
        args.quality, 
        args.verbose
    )
    
    if success and Path(args.output).exists():
        converter.print_file_info(args.output)
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())