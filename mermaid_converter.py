#!/usr/bin/env python3
"""
Mermaid图表转换工具
支持转换为PNG格式，并可选择在终端打印质量信息
"""

import os
import sys
import subprocess
from pathlib import Path

class MermaidConverter:
    def __init__(self):
        self.supported_formats = ['png', 'svg', 'pdf']
        self.quality_levels = {
            'low': {'scale': 1, 'width': 800},
            'medium': {'scale': 2, 'width': 1200}, 
            'high': {'scale': 3, 'width': 1600},
            'ultra': {'scale': 4, 'width': 2400}
        }
    
    def check_mermaid_cli(self) -> bool:
        """检查是否安装了mermaid-cli"""
        try:
            # 在Windows上尝试不同的命令形式
            commands_to_try = ['mmdc', 'mmdc.cmd']

            for cmd in commands_to_try:
                try:
                    result = subprocess.run([cmd, '--version'],
                                          capture_output=True, text=True, shell=True)
                    if result.returncode == 0:
                        return True
                except:
                    continue
            return False
        except Exception:
            return False
    
    def install_mermaid_cli(self) -> bool:
        """尝试安装mermaid-cli"""
        print("正在安装 mermaid-cli...")
        try:
            subprocess.run(['npm', 'install', '-g', '@mermaid-js/mermaid-cli'], 
                          check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ 安装失败。请手动安装:")
            print("npm install -g @mermaid-js/mermaid-cli")
            return False
    
    def convert_to_png(self, input_file: str, output_file: str, 
                      quality: str = 'medium', verbose: bool = False) -> bool:
        """转换Mermaid文件为PNG"""
        
        if not Path(input_file).exists():
            print(f"❌ 输入文件不存在: {input_file}")
            return False
        
        # 检查mermaid-cli
        if not self.check_mermaid_cli():
            print("❌ 未找到 mermaid-cli")
            if not self.install_mermaid_cli():
                return False
        
        # 获取质量参数
        quality_config = self.quality_levels.get(quality, self.quality_levels['medium'])
        
        # 构建命令
        cmd = [
            'mmdc',
            '-i', input_file,
            '-o', output_file,
            '-s', str(quality_config['scale']),
            '-w', str(quality_config['width']),
            '--backgroundColor', 'white'
        ]

        try:
            if verbose:
                print(f"🔄 执行命令: {' '.join(cmd)}")

            # 在Windows上使用shell=True来执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                file_size = Path(output_file).stat().st_size
                print(f"✅ 转换成功!")
                print(f"📁 输出文件: {output_file}")
                print(f"📊 文件大小: {file_size / 1024:.1f} KB")
                print(f"🎨 质量等级: {quality}")
                print(f"📐 分辨率: {quality_config['width']}px (scale: {quality_config['scale']})")
                return True
            else:
                print(f"❌ 转换失败:")
                print(f"错误信息: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 执行出错: {e}")
            return False
    
    def print_file_info(self, file_path: str):
        """打印文件信息"""
        if not Path(file_path).exists():
            print(f"❌ 文件不存在: {file_path}")
            return
        
        path = Path(file_path)
        stat = path.stat()
        
        print(f"\n📄 文件信息:")
        print(f"   文件名: {path.name}")
        print(f"   路径: {path.absolute()}")
        print(f"   大小: {stat.st_size / 1024:.1f} KB")
        print(f"   修改时间: {stat.st_mtime}")
        
        # 读取文件内容统计
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.count('\n') + 1
                chars = len(content)
                print(f"   行数: {lines}")
                print(f"   字符数: {chars}")
        except Exception as e:
            print(f"   读取失败: {e}")

def select_input_file():
    """交互式选择输入文件"""
    print("\n🔍 选择输入文件:")

    # 扫描当前目录的mermaid文件
    current_dir = Path('.')
    mermaid_files = []

    # 常见的mermaid文件扩展名
    extensions = ['.mmd', '.mermaid', '.txt', '.md']

    for ext in extensions:
        mermaid_files.extend(current_dir.glob(f'*{ext}'))

    if not mermaid_files:
        print("❌ 当前目录没有找到Mermaid文件")
        manual_input = input("请手动输入文件路径: ").strip()
        return manual_input if manual_input else None

    print("找到以下文件:")
    for i, file in enumerate(mermaid_files, 1):
        print(f"  {i}. {file.name}")

    print(f"  {len(mermaid_files) + 1}. 手动输入路径")

    while True:
        try:
            choice = input(f"\n请选择文件 (1-{len(mermaid_files) + 1}): ").strip()
            if not choice:
                continue

            choice_num = int(choice)
            if 1 <= choice_num <= len(mermaid_files):
                return str(mermaid_files[choice_num - 1])
            elif choice_num == len(mermaid_files) + 1:
                manual_input = input("请输入文件路径: ").strip()
                return manual_input if manual_input else None
            else:
                print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入数字")

def select_quality():
    """交互式选择图片质量"""
    print("\n🎨 选择图片质量:")
    qualities = ['low', 'medium', 'high', 'ultra']
    descriptions = {
        'low': '低质量 (800px, scale=1) - 文件小，速度快',
        'medium': '中等质量 (1200px, scale=2) - 平衡选择',
        'high': '高质量 (1600px, scale=3) - 清晰度好',
        'ultra': '超高质量 (2400px, scale=4) - 最佳效果，文件大'
    }

    for i, quality in enumerate(qualities, 1):
        print(f"  {i}. {quality.upper()} - {descriptions[quality]}")

    while True:
        try:
            choice = input(f"\n请选择质量 (1-{len(qualities)}) [默认: 2]: ").strip()
            if not choice:
                return 'medium'

            choice_num = int(choice)
            if 1 <= choice_num <= len(qualities):
                return qualities[choice_num - 1]
            else:
                print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入数字")

def select_output_file(input_file):
    """交互式选择输出文件"""
    print("\n📁 选择输出文件:")

    # 默认输出文件名
    input_path = Path(input_file)
    default_output = str(input_path.with_suffix('.png'))

    print(f"  1. 使用默认名称: {default_output}")
    print(f"  2. 自定义文件名")

    while True:
        try:
            choice = input("\n请选择 (1-2) [默认: 1]: ").strip()
            if not choice or choice == '1':
                return default_output
            elif choice == '2':
                custom_name = input("请输入输出文件名 (包含.png扩展名): ").strip()
                if custom_name:
                    if not custom_name.endswith('.png'):
                        custom_name += '.png'
                    return custom_name
                else:
                    return default_output
            else:
                print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入数字")

def select_options():
    """交互式选择其他选项"""
    print("\n⚙️ 其他选项:")
    print("  1. 显示详细信息 (verbose)")
    print("  2. 显示文件信息 (info)")
    print("  3. 两者都要")
    print("  4. 都不需要")

    while True:
        try:
            choice = input("\n请选择 (1-4) [默认: 4]: ").strip()
            if not choice or choice == '4':
                return False, False
            elif choice == '1':
                return True, False
            elif choice == '2':
                return False, True
            elif choice == '3':
                return True, True
            else:
                print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入数字")

def main():
    print("=" * 60)
    print("🎨 Mermaid图表转PNG工具")
    print("=" * 60)

    converter = MermaidConverter()

    # 交互式选择配置
    input_file = select_input_file()
    if not input_file:
        print("❌ 未选择输入文件，退出程序")
        return 1

    quality = select_quality()
    output_file = select_output_file(input_file)
    verbose, show_info = select_options()

    print("\n" + "=" * 60)
    print("📋 配置总结:")
    print(f"   输入文件: {input_file}")
    print(f"   输出文件: {output_file}")
    print(f"   图片质量: {quality.upper()}")
    print(f"   详细信息: {'是' if verbose else '否'}")
    print(f"   文件信息: {'是' if show_info else '否'}")
    print("=" * 60)

    # 确认开始转换
    confirm = input("\n开始转换? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("❌ 用户取消转换")
        return 0

    # 显示文件信息
    if show_info:
        converter.print_file_info(input_file)

    # 转换文件
    print(f"\n🚀 开始转换...")
    success = converter.convert_to_png(
        input_file,
        output_file,
        quality,
        verbose
    )

    if success and Path(output_file).exists():
        print(f"\n📊 输出文件信息:")
        converter.print_file_info(output_file)

    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())