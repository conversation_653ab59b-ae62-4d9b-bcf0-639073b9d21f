graph TD
    %% 启动阶段
    A[用户启动 start_pdf_bao.py] --> B{检查依赖}
    B -->|缺少依赖| C[显示错误信息<br/>提示安装依赖]
    B -->|依赖完整| D[创建 ModernPDFBaoGUI 实例]
    
    %% GUI初始化
    D --> E[初始化GUI界面]
    E --> E1[设置主题和外观<br/>创建主窗口]
    E1 --> E2[创建顶部标题栏<br/>Logo + 标题 + 主题切换]
    E2 --> E3[创建侧边栏导航<br/>文件管理/翻译设置/高级选项/进度/日志]
    E3 --> E4[创建主内容区域<br/>多标签页界面]
    E4 --> E5[加载.env配置<br/>初始化配置变量]
    E5 --> F[GUI界面就绪<br/>等待用户操作]
    
    %% 用户操作阶段
    F --> G{用户操作类型}
    
    %% 文件选择分支
    G -->|选择文件| H[文件管理标签页]
    H --> H1[支持拖拽或浏览选择]
    H1 --> H2[验证PDF文件格式]
    H2 -->|无效文件| H3[显示错误提示]
    H2 -->|有效文件| H4[添加到文件列表<br/>显示文件信息]
    H4 --> F
    
    %% 配置设置分支
    G -->|配置设置| I[翻译设置标签页]
    I --> I1[API配置<br/>base_url, api_key, model]
    I1 --> I2[语言设置<br/>源语言/目标语言]
    I2 --> I3[翻译参数<br/>QPS限制/线程数/最小文本长度]
    I3 --> I4[输出设置<br/>输出目录/水印模式/双语选项]
    I4 --> I5[高级选项<br/>公式处理/OCR设置/字体选择]
    I5 --> I6[词汇表设置<br/>加载CSV词典文件]
    I6 --> F
    
    %% 开始翻译分支
    G -->|开始翻译| J{验证配置}
    J -->|配置无效| K[显示配置错误<br/>高亮问题字段]
    J -->|配置有效| L[创建翻译线程<br/>开始翻译流程]
    
    %% 翻译核心流程
    L --> M[初始化翻译环境]
    M --> M1[创建 TranslationConfig 对象]
    M1 --> M2[初始化 OpenAITranslator]
    M2 --> M3[设置速率限制器<br/>RateLimiter]
    M3 --> M4[初始化文档布局模型<br/>DocLayoutModel]
    M4 --> M5[加载词汇表<br/>Glossary.from_csv]
    M5 --> M6[创建进度监控器<br/>ProgressMonitor]
    M6 --> N[开始处理文件队列]
    
    %% 文件处理循环
    N --> N1{还有文件待处理?}
    N1 -->|否| END1[翻译完成<br/>显示统计信息]
    N1 -->|是| N2[取出下一个文件]
    N2 --> N3[启动时间统计<br/>start_file]
    N3 --> O[进入PDF处理流水线]
    
    %% PDF处理流水线 - 阶段1: IL创建
    O --> P1[阶段1: ILCreater<br/>解析PDF创建中间表示]
    P1 --> P1a[使用PyMuPDF打开PDF文档]
    P1a --> P1b[提取页面信息<br/>文本/字体/图像/路径]
    P1b --> P1c[创建文档IL结构<br/>Document/Page/TextBlock]
    P1c --> P1d[处理字符级信息<br/>位置/字体/大小/颜色]
    P1d --> P2
    
    %% 阶段2: 扫描文档检测
    P2[阶段2: DetectScannedFile<br/>检测扫描文档] --> P2a[分析文档特征<br/>文本密度/图像比例]
    P2a --> P2b{是否为扫描文档?}
    P2b -->|是| P2c[启用OCR处理模式<br/>设置ocr_workaround标志]
    P2b -->|否| P2d[使用标准文本提取]
    P2c --> P3
    P2d --> P3
    
    %% 阶段3: 布局解析
    P3[阶段3: LayoutParser<br/>解析页面布局] --> P3a[使用ONNX模型分析布局<br/>DocLayoutModel.detect]
    P3a --> P3b[识别文档元素<br/>标题/段落/图片/表格/公式]
    P3b --> P3c[计算元素边界框<br/>坐标/宽度/高度]
    P3c --> P3d[建立元素层次关系<br/>父子/兄弟关系]
    P3d --> P4
    
    %% 阶段4: 表格解析
    P4[阶段4: TableParser<br/>解析表格内容] --> P4a{是否启用表格翻译?}
    P4a -->|否| P5
    P4a -->|是| P4b[使用RapidOCR识别表格<br/>提取表格结构和内容]
    P4b --> P4c[解析表格单元格<br/>行列信息/合并单元格]
    P4c --> P4d[标记可翻译文本<br/>过滤数字/公式]
    P4d --> P5
    
    %% 阶段5: 段落识别
    P5[阶段5: ParagraphFinder<br/>识别段落结构] --> P5a[分析文本块关系<br/>行间距/对齐方式]
    P5a --> P5b[合并相关文本行<br/>形成完整段落]
    P5b --> P5c[识别段落类型<br/>正文/标题/列表/引用]
    P5c --> P5d[处理跨页段落<br/>页面边界处理]
    P5d --> P6
    
    %% 阶段6: 样式和公式处理
    P6[阶段6: StylesAndFormulas<br/>处理样式和公式] --> P6a[识别数学公式<br/>字体模式匹配]
    P6a --> P6b[标记公式区域<br/>避免翻译数学表达式]
    P6b --> P6c[分析文本样式<br/>粗体/斜体/字号]
    P6c --> P6d[保存样式信息<br/>用于后续排版]
    P6d --> P7
    
    %% 阶段7: 自动术语提取
    P7[阶段7: AutomaticTermExtractor<br/>自动提取术语] --> P7a{是否启用自动术语提取?}
    P7a -->|否| P8
    P7a -->|是| P7b[扫描文档识别专业术语<br/>使用NLP技术]
    P7b --> P7c[与现有词汇表对比<br/>避免重复]
    P7c --> P7d[构建临时术语词典<br/>用于翻译一致性]
    P7d --> P8
    
    %% 阶段8: 翻译处理
    P8[阶段8: ILTranslator<br/>翻译段落内容] --> P8a[准备翻译上下文<br/>系统提示/术语词典]
    P8a --> P8b[分批处理段落<br/>考虑API限制]
    P8b --> P8c[调用翻译API<br/>OpenAI GPT模型]
    
    %% 翻译API调用详细流程
    P8c --> T1[检查翻译缓存<br/>TranslationCache.get]
    T1 --> T2{缓存命中?}
    T2 -->|是| T3[返回缓存结果<br/>更新统计计数]
    T2 -->|否| T4[应用速率限制<br/>RateLimiter.wait]
    T4 --> T5[构建翻译请求<br/>包含上下文和术语]
    T5 --> T6[发送API请求<br/>httpx.post]
    T6 --> T7{请求成功?}
    T7 -->|否| T8[重试机制<br/>tenacity装饰器]
    T8 --> T6
    T7 -->|是| T9[解析响应结果<br/>提取翻译文本]
    T9 --> T10[保存到缓存<br/>TranslationCache.set]
    T10 --> T11[更新token统计<br/>prompt/completion计数]
    T3 --> P8d
    T11 --> P8d
    
    P8d[应用术语词典<br/>确保术语一致性] --> P8e[后处理翻译结果<br/>格式清理/特殊字符]
    P8e --> P9
    
    %% 阶段9: 排版处理
    P9[阶段9: Typesetting<br/>重新排版] --> P9a[计算文本布局<br/>考虑字体变化]
    P9a --> P9b[调整行间距和字间距<br/>保持美观性]
    P9b --> P9c[处理文本溢出<br/>自动换行/缩放]
    P9c --> P9d[保持原始样式<br/>粗体/斜体/颜色]
    P9d --> P10
    
    %% 阶段10: 字体映射
    P10[阶段10: FontMapper<br/>添加字体支持] --> P10a[分析目标语言字体需求<br/>中文/日文/韩文]
    P10a --> P10b[选择合适字体<br/>serif/sans-serif/script]
    P10b --> P10c[创建字体子集<br/>只包含使用的字符]
    P10c --> P10d[嵌入字体到PDF<br/>确保跨平台兼容]
    P10d --> P11
    
    %% 阶段11: PDF创建
    P11[阶段11: PDFCreater<br/>生成绘制指令] --> P11a[创建新PDF文档<br/>PyMuPDF.Document]
    P11a --> P11b[逐页生成内容<br/>文本/图像/路径]
    P11b --> P11c[应用变换矩阵<br/>位置/旋转/缩放]
    P11c --> P11d[处理特殊元素<br/>链接/注释/书签]
    P11d --> P12
    
    %% 阶段12: 字体子集化
    P12[阶段12: 字体子集化<br/>SUBSET_FONT_STAGE] --> P12a[分析字体使用情况<br/>统计使用的字符]
    P12a --> P12b[创建最小字体子集<br/>减少文件大小]
    P12b --> P12c[更新字体引用<br/>修正字体映射]
    P12c --> P13
    
    %% 阶段13: 保存PDF
    P13[阶段13: 保存PDF<br/>SAVE_PDF_STAGE] --> P13a{输出模式选择}
    P13a -->|单语模式| P13b[保存翻译版PDF<br/>mono_pdf_path]
    P13a -->|双语模式| P13c[创建双语对照PDF<br/>dual_pdf_path]
    P13a -->|交替页面模式| P13d[原文译文交替排列<br/>alternating_pages_dual]
    P13b --> P13e
    P13c --> P13e
    P13d --> P13e
    
    P13e{水印设置} --> P13f[添加PDF-Bao水印<br/>版本信息]
    P13e -->|无水印模式| P13g[保存无水印版本<br/>no_watermark版本]
    P13f --> P13h[修复字符映射<br/>fix_cmap函数]
    P13g --> P13h
    P13h --> P14[文件处理完成<br/>更新进度统计]
    
    %% 完成单个文件处理
    P14 --> P15[结束文件时间统计<br/>end_file]
    P15 --> P16[更新GUI进度显示<br/>文件完成状态]
    P16 --> N1
    
    %% 错误处理流程
    P1 -.->|异常| ERR1[捕获异常<br/>记录错误日志]
    P2 -.->|异常| ERR1
    P3 -.->|异常| ERR1
    P4 -.->|异常| ERR1
    P5 -.->|异常| ERR1
    P6 -.->|异常| ERR1
    P7 -.->|异常| ERR1
    P8 -.->|异常| ERR1
    P9 -.->|异常| ERR1
    P10 -.->|异常| ERR1
    P11 -.->|异常| ERR1
    P12 -.->|异常| ERR1
    P13 -.->|异常| ERR1
    
    ERR1 --> ERR2[显示错误信息<br/>在GUI日志区域]
    ERR2 --> ERR3{用户选择}
    ERR3 -->|继续| N1
    ERR3 -->|停止| END2[翻译中断<br/>清理资源]
    
    %% 最终完成
    END1 --> FINAL1[显示翻译完成通知<br/>统计信息汇总]
    FINAL1 --> FINAL2[生成性能报告<br/>时间/token/缓存统计]
    FINAL2 --> FINAL3[清理临时文件<br/>释放资源]
    FINAL3 --> F
    
    END2 --> FINAL3
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    
    class A,END1,END2,FINAL3 startEnd
    class P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13 process
    class B,G,J,N1,P2b,P4a,P7a,T2,T7,P13a,P13e,ERR3 decision
    class ERR1,ERR2,C,K error